<!--
  过滤器管理页面
  集成到现有的后台管理系统中
-->
<template>
  <div class="filter-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">过滤器管理</h1>
        <p class="page-description">管理内容筛选的过滤器标签，支持多维度分类</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><i-ep-plus /></el-icon>
          添加过滤器
        </el-button>
        <el-button @click="loadFilters" :loading="loading">
          <el-icon><i-ep-refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 过滤器类型标签 -->
    <div class="filter-types">
      <el-tag v-for="type in filterTypes" :key="type.key" :type="selectedType === type.key ? 'primary' : 'info'"
        :effect="selectedType === type.key ? 'dark' : 'plain'" @click="selectType(type.key)" class="type-tag">
        {{ type.name }} ({{ getTypeCount(type.key) }})
      </el-tag>
    </div>

    <!-- 过滤器列表 -->
    <div class="filters-container" v-loading="loading">
      <div v-if="filteredFilters.length === 0" class="empty-state">
        <el-empty description="暂无过滤器数据">
          <el-button type="primary" @click="showCreateDialog = true">
            添加第一个过滤器
          </el-button>
        </el-empty>
      </div>

      <div v-else class="filters-grid">
        <div v-for="filter in filteredFilters" :key="filter.id" class="filter-card"
          :class="{ 'inactive': !filter.isActive }">
          <!-- 过滤器信息 -->
          <div class="filter-info">
            <div class="filter-header">
              <h3 class="filter-name">{{ filter.name }}</h3>
              <div class="filter-actions">
                <el-button type="primary" size="small" @click="editFilter(filter)" link>
                  编辑
                </el-button>
                <el-button :type="filter.isActive ? 'warning' : 'success'" size="small"
                  @click="toggleFilterStatus(filter)" link>
                  {{ filter.isActive ? '禁用' : '启用' }}
                </el-button>
                <el-button type="danger" size="small" @click="deleteFilter(filter)" link>
                  删除
                </el-button>
              </div>
            </div>

            <div class="filter-details">
              <div class="detail-item">
                <span class="label">类型:</span>
                <el-tag size="small" type="info">{{ getTypeName(filter.type) }}</el-tag>
              </div>
              <div class="detail-item">
                <span class="label">键值:</span>
                <code class="filter-key">{{ filter.key }}</code>
              </div>
              <div class="detail-item">
                <span class="label">语言:</span>
                <div class="filter-languages">
                  <el-tag v-if="!filter.languages || filter.languages.length === 0" size="small" type="info">
                    通用
                  </el-tag>
                  <el-tag v-else v-for="lang in filter.languages" :key="lang" size="small" type="success"
                    class="language-tag">
                    {{ getLanguageName(lang) }}
                  </el-tag>
                </div>
              </div>
              <div v-if="filter.description" class="detail-item">
                <span class="label">描述:</span>
                <span class="filter-description">{{ filter.description }}</span>
              </div>
              <div class="detail-item">
                <span class="label">创建时间:</span>
                <span class="filter-time">{{ formatTime(filter.createdAt) }}</span>
              </div>
              <div v-if="filter.updatedAt !== filter.createdAt" class="detail-item">
                <span class="label">更新时间:</span>
                <span class="filter-time">{{ formatTime(filter.updatedAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 状态指示器 -->
          <div class="filter-status">
            <el-tag :type="filter.isActive ? 'success' : 'danger'" size="small">
              {{ filter.isActive ? '启用' : '禁用' }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <FilterEditDialog v-model="showEditDialog" :filter="editingFilter" :filter-types="filterTypes"
      @save="handleSaveFilter" />

    <!-- 创建对话框 -->
    <FilterCreateDialog v-model="showCreateDialog" :filter-types="filterTypes" @create="handleCreateFilter" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import filterService from '@/services/filterService'
import dimensionService from '@/services/dimensionService'
import FilterEditDialog from '@/components/admin/FilterEditDialog.vue'
import FilterCreateDialog from '@/components/admin/FilterCreateDialog.vue'
import { getLanguageLabel } from '@/config/languages'

// 响应式数据
const loading = ref(false)
const filters = ref([])
const selectedType = ref('all')
const showEditDialog = ref(false)
const showCreateDialog = ref(false)
const editingFilter = ref(null)

// 过滤器类型配置
const filterTypes = ref([
  { key: 'all', name: '全部' }
])

// 计算属性
const filteredFilters = computed(() => {
  if (selectedType.value === 'all') {
    return filters.value
  }
  return filters.value.filter(filter => filter.type === selectedType.value)
})

// 获取类型数量
const getTypeCount = (type) => {
  if (type === 'all') {
    return filters.value.length
  }
  return filters.value.filter(filter => filter.type === type).length
}

// 获取类型名称
const getTypeName = (type) => {
  const typeConfig = filterTypes.value.find(t => t.key === type)
  return typeConfig ? typeConfig.name : type
}

// 获取语言名称
const getLanguageName = (langCode) => {
  return getLanguageLabel(langCode)
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return '-'
  const date = new Date(timeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 选择类型
const selectType = (type) => {
  selectedType.value = type
}

// 加载过滤器列表和维度类型
const loadFilters = async () => {
  loading.value = true
  try {
    const [filtersResponse, dimensionsResponse] = await Promise.all([
      filterService.getAllFilters(),
      dimensionService.getAllDimensions()
    ])

    filters.value = filtersResponse.filters || filtersResponse.data || []

    // 动态更新过滤器类型列表
    const dynamicTypes = dimensionsResponse.dimensions?.map(dim => ({
      key: dim.type,
      name: dim.name
    })) || []

    filterTypes.value = [
      { key: 'all', name: '全部' },
      ...dynamicTypes
    ]
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 编辑过滤器
const editFilter = (filter) => {
  editingFilter.value = { ...filter }
  showEditDialog.value = true
}

// 切换过滤器状态
const toggleFilterStatus = async (filter) => {
  try {
    const newStatus = !filter.isActive
    await filterService.updateFilter(filter.id, { isActive: newStatus })
    filter.isActive = newStatus
    ElMessage.success(`过滤器已${newStatus ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('更新过滤器状态失败:', error)
    ElMessage.error('更新失败')
  }
}

// 删除过滤器
const deleteFilter = async (filter) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除过滤器"${filter.name}"吗？此操作不可恢复。`,
      '确认删除',
      { type: 'warning' }
    )

    await filterService.deleteFilter(filter.id)
    await loadFilters()
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除过滤器失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 处理保存过滤器
const handleSaveFilter = async (filterData) => {
  try {
    await filterService.updateFilter(editingFilter.value.id, filterData)
    await loadFilters()
    showEditDialog.value = false
    editingFilter.value = null
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存过滤器失败:', error)
    ElMessage.error('保存失败')
  }
}

// 处理创建过滤器
const handleCreateFilter = async (filterData) => {
  try {
    await filterService.createFilter(filterData)
    await loadFilters()
    showCreateDialog.value = false
    ElMessage.success('创建成功')
  } catch (error) {
    console.error('创建过滤器失败:', error)
    ElMessage.error('创建失败')
  }
}

// 页面初始化
onMounted(() => {
  loadFilters()
})
</script>

<style scoped>
.filter-management-page {
  padding: 1.5rem;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
}

.filter-types {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.type-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.type-tag:hover {
  transform: scale(1.05);
}

.filters-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1rem;
}

.filter-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
  position: relative;
}

.filter-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-card.inactive {
  opacity: 0.6;
  background: var(--el-bg-color-page);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.filter-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  flex: 1;
}

.filter-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.filter-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.label {
  color: var(--el-text-color-secondary);
  font-weight: 500;
  min-width: 3rem;
}

.filter-key {
  background: var(--el-bg-color-page);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
}

.filter-languages {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.language-tag {
  margin: 0;
}

.filter-description {
  color: var(--el-text-color-regular);
}

.filter-time {
  color: var(--el-text-color-placeholder);
  font-size: 0.8rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.filter-status {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-management-page {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .filter-header {
    flex-direction: column;
    gap: 0.5rem;
  }

  .filter-actions {
    justify-content: flex-start;
  }
}
</style>
