/**
 * 统一过滤器服务
 * 提供过滤器的API调用，支持语言等级、内容类型、主题、教材等过滤器
 */

import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

class FilterService {
  constructor() {
    this.baseUrl = "/api/filters";
  }

  /**
   * 获取所有过滤器（按类型分组）
   * @param {Object} options 查询选项
   * @returns {Promise} API响应
   */
  async getFilters(options = {}) {
    const params = new URLSearchParams();

    if (options.language) {
      params.append("language", options.language);
    }
    if (options.type) {
      params.append("type", options.type);
    }
    if (options._t) {
      params.append("_t", options._t);
    }

    const url = `${this.baseUrl}${
      params.toString() ? "?" + params.toString() : ""
    }`;
    return await httpClient.get(url);
  }

  /**
   * 获取语言等级过滤器
   * @param {string} language 语言代码
   * @returns {Promise} API响应
   */
  async getLanguageLevels(language) {
    if (!language) {
      throw new Error("语言参数是必需的");
    }
    return await httpClient.get(
      `${this.baseUrl}/language-levels?language=${language}`
    );
  }

  /**
   * 获取内容类型过滤器
   * @returns {Promise} API响应
   */
  async getContentTypes() {
    return await httpClient.get(`${this.baseUrl}/content-types`);
  }

  /**
   * 获取主题过滤器
   * @returns {Promise} API响应
   */
  async getTopics() {
    return await httpClient.get(`${this.baseUrl}/topics`);
  }

  /**
   * 获取教材过滤器
   * @param {string} language 语言代码（可选）
   * @returns {Promise} API响应
   */
  async getMaterials(language = null) {
    const params = language ? `?language=${language}` : "";
    return await httpClient.get(`${this.baseUrl}/materials${params}`);
  }

  /**
   * 获取内容的过滤器
   * @param {string} contentId 内容ID
   * @returns {Promise} API响应
   */
  async getContentFilters(contentId) {
    return await httpClient.get(`${this.baseUrl}/content/${contentId}`);
  }

  /**
   * 为内容设置过滤器
   * @param {string} contentId 内容ID
   * @param {Array} filterIds 过滤器ID数组
   * @returns {Promise} API响应
   */
  async setContentFilters(contentId, filterIds) {
    return await httpClient.post(`${this.baseUrl}/content/${contentId}`, {
      filterIds,
    });
  }

  /**
   * 根据过滤器获取内容
   * @param {Array} filterIds 过滤器ID数组
   * @param {Object} options 查询选项
   * @returns {Promise} API响应
   */
  async getContentsByFilters(filterIds, options = {}) {
    const params = new URLSearchParams();

    params.append("filters", filterIds.join(","));

    if (options.page) {
      params.append("page", options.page);
    }
    if (options.pageSize) {
      params.append("pageSize", options.pageSize);
    }
    if (options.sortBy) {
      params.append("sortBy", options.sortBy);
    }
    if (options.sortOrder) {
      params.append("sortOrder", options.sortOrder);
    }

    return await httpClient.get(
      `${this.baseUrl}/contents?${params.toString()}`
    );
  }

  // ==================== 后台管理功能 ====================

  /**
   * 创建新过滤器
   * @param {Object} filterData 过滤器数据
   * @returns {Promise} API响应
   */
  async createFilter(filterData) {
    return await httpClient.post(this.baseUrl, filterData);
  }

  /**
   * 更新过滤器
   * @param {number} filterId 过滤器ID
   * @param {Object} filterData 过滤器数据
   * @returns {Promise} API响应
   */
  async updateFilter(filterId, filterData) {
    return await httpClient.put(`${this.baseUrl}/${filterId}`, filterData);
  }

  /**
   * 删除过滤器
   * @param {number} filterId 过滤器ID
   * @returns {Promise} API响应
   */
  async deleteFilter(filterId) {
    return await httpClient.delete(`${this.baseUrl}/${filterId}`);
  }

  /**
   * 批量更新过滤器状态
   * @param {Array} ids 过滤器ID数组
   * @param {boolean} isActive 是否激活
   * @returns {Promise} API响应
   */
  async batchUpdateStatus(ids, isActive) {
    return await httpClient.patch(`${this.baseUrl}/batch-status`, {
      ids,
      isActive,
    });
  }

  // ==================== 过滤器类型管理 ====================

  /**
   * 获取所有过滤器类型
   * @returns {Promise} API响应
   */
  async getFilterTypes() {
    return await httpClient.get(`${this.baseUrl}/types`);
  }

  /**
   * 创建新的过滤器类型
   * @param {Object} typeData 过滤器类型数据
   * @returns {Promise} API响应
   */
  async createFilterType(typeData) {
    return await httpClient.post(`${this.baseUrl}/types`, typeData);
  }

  /**
   * 更新过滤器类型
   * @param {number} typeId 过滤器类型ID
   * @param {Object} typeData 过滤器类型数据
   * @returns {Promise} API响应
   */
  async updateFilterType(typeId, typeData) {
    return await httpClient.put(`${this.baseUrl}/types/${typeId}`, typeData);
  }

  /**
   * 删除过滤器类型
   * @param {number} typeId 过滤器类型ID
   * @returns {Promise} API响应
   */
  async deleteFilterType(typeId) {
    return await httpClient.delete(`${this.baseUrl}/types/${typeId}`);
  }

  // ==================== 便捷方法 ====================

  /**
   * 获取按语言分组的过滤器
   * @param {string} language 语言代码
   * @returns {Promise} 处理后的过滤器数据
   */
  async getFiltersByLanguage(language) {
    try {
      const response = await this.getFilters({ language });
      if (response.success) {
        return response.data;
      }
      throw new Error(response.message || "获取过滤器失败");
    } catch (error) {
      console.error("获取语言过滤器失败:", error);
      throw error;
    }
  }

  /**
   * 获取扁平化的过滤器列表
   * @param {Object} options 查询选项
   * @returns {Promise} 扁平化的过滤器数组
   */
  async getFlatFilters(options = {}) {
    try {
      const response = await this.getFilters(options);
      if (response.success) {
        const flatFilters = [];
        Object.values(response.data).forEach((typeFilters) => {
          flatFilters.push(...typeFilters);
        });
        return flatFilters;
      }
      throw new Error(response.message || "获取过滤器失败");
    } catch (error) {
      console.error("获取扁平过滤器失败:", error);
      throw error;
    }
  }

  /**
   * 根据ID获取过滤器信息
   * @param {Array} filterIds 过滤器ID数组
   * @returns {Promise} 过滤器信息数组
   */
  async getFiltersByIds(filterIds) {
    try {
      const flatFilters = await this.getFlatFilters();
      return flatFilters.filter((filter) => filterIds.includes(filter.id));
    } catch (error) {
      console.error("根据ID获取过滤器失败:", error);
      throw error;
    }
  }
  /**
   * 获取所有过滤器（包括禁用的，用于管理）
   * @returns {Promise} API响应
   */
  async getAllFilters() {
    return await httpClient.get(`${API_ENDPOINTS.ADMIN.FILTERS}/all`);
  }

  /**
   * 创建过滤器
   * @param {Object} filterData 过滤器数据
   * @returns {Promise} API响应
   */
  async createFilter(filterData) {
    return await httpClient.post(API_ENDPOINTS.ADMIN.FILTERS, filterData);
  }

  /**
   * 更新过滤器
   * @param {number} filterId 过滤器ID
   * @param {Object} filterData 过滤器数据
   * @returns {Promise} API响应
   */
  async updateFilter(filterId, filterData) {
    return await httpClient.put(
      `${API_ENDPOINTS.ADMIN.FILTERS}/${filterId}`,
      filterData
    );
  }

  /**
   * 删除过滤器
   * @param {number} filterId 过滤器ID
   * @returns {Promise} API响应
   */
  async deleteFilter(filterId) {
    return await httpClient.delete(
      `${API_ENDPOINTS.ADMIN.FILTERS}/${filterId}`
    );
  }

  /**
   * 批量更新过滤器状态
   * @param {Array} filterIds 过滤器ID数组
   * @param {boolean} isActive 是否启用
   * @returns {Promise} API响应
   */
  async batchUpdateStatus(filterIds, isActive) {
    return await httpClient.patch(
      `${API_ENDPOINTS.ADMIN.FILTERS}/batch-status`,
      {
        filterIds,
        isActive,
      }
    );
  }
}

export default new FilterService();
