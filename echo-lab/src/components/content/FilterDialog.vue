<!--
  筛选弹窗组件
  电脑端：弹窗，手机端：抽屉
  上方：维度选择，下方：标签展示
  底部：取消、清空、确定按钮
-->
<template>
  <div class="filter-dialog-wrapper">
    <!-- 触发按钮 -->
    <el-button @click="showDialog = true" class="filter-trigger-btn">
      <el-icon><i-ep-filter /></el-icon>
      筛选
      <el-badge v-if="selectedCount > 0" :value="selectedCount" :max="99" />
    </el-button>

    <!-- 电脑端：标准弹窗 -->
    <StandardDialog
      v-if="!isMobile"
      v-model="showDialog"
      title="内容筛选"
      width="700px"
      :close-on-click-modal="false"
      :extra-buttons="[
        { text: '清空', type: 'default', closeDialog: false }
      ]"
      cancel-text="取消"
      confirm-text="确定"
      :show-confirm="true"
      @close="cancelSelection"
      @confirm="confirmSelection"
      @extra-button-click="handleExtraButton"
      class="filter-dialog"
    >
      <div class="dialog-content" v-loading="loading">
        <!-- 选择统计信息 -->
        <div class="selection-summary">
          <span class="total-selected">已选择 {{ selectedCount }} 个筛选条件</span>
        </div>

        <!-- 所有维度展示 -->
        <div class="all-dimensions-section">
          <div
            v-for="dimension in availableDimensions"
            :key="dimension.type"
            class="dimension-section"
          >
            <div class="section-title">
              {{ dimension.name }}
              <span class="selected-hint" v-if="getDimensionSelectedCount(dimension.type) > 0">
                (已选{{ getDimensionSelectedCount(dimension.type) }}个)
              </span>
            </div>
            <div class="tags-container">
              <el-tag
                v-for="filter in getDimensionFilters(dimension.type)"
                :key="filter.id"
                :type="isSelected(filter.id) ? 'primary' : undefined"
                :effect="isSelected(filter.id) ? 'dark' : 'plain'"
                @click="toggleFilter(filter.id, dimension.type)"
                class="filter-tag"
                :class="{ 'selected': isSelected(filter.id) }"
              >
                {{ filter.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </StandardDialog>

    <!-- 手机端：抽屉 -->
    <el-drawer
      v-else
      v-model="showDialog"
      title="内容筛选"
      direction="btt"
      size="75%"
      :close-on-click-modal="false"
      class="filter-drawer"
    >
      <div class="drawer-content" v-loading="loading">
        <!-- 所有维度展示 -->
        <div class="all-dimensions-section mobile-dimensions">
          <div
            v-for="dimension in availableDimensions"
            :key="dimension.type"
            class="dimension-section mobile-dimension-section"
          >
            <div class="section-title">
              {{ dimension.name }}
              <span class="selected-hint" v-if="getDimensionSelectedCount(dimension.type) > 0">
                (已选{{ getDimensionSelectedCount(dimension.type) }}个)
              </span>
            </div>
            <div class="tags-container mobile-tags">
              <el-tag
                v-for="filter in getDimensionFilters(dimension.type)"
                :key="filter.id"
                :type="isSelected(filter.id) ? 'primary' : undefined"
                :effect="isSelected(filter.id) ? 'dark' : 'plain'"
                @click="toggleFilter(filter.id, dimension.type)"
                class="filter-tag mobile-filter-tag"
                :class="{ 'selected': isSelected(filter.id) }"
              >
                {{ filter.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <template #footer>
        <div class="drawer-footer">
          <div class="footer-info">
            <span class="total-selected">已选择 {{ selectedCount }} 个筛选条件</span>
          </div>
          <div class="footer-actions">
            <el-button @click="cancelSelection" class="footer-btn">取消</el-button>
            <el-button @click="clearSelection" plain class="footer-btn">清空</el-button>
            <el-button type="primary" @click="confirmSelection" class="footer-btn">确定</el-button>
          </div>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { isMobileDevice } from '@/utils/deviceDetector'
import { useLanguageStore } from '@/stores/languageStore'
import filterService from '@/services/filterService'
import dimensionService from '@/services/dimensionService'
import StandardDialog from '@/components/common/StandardDialog.vue'

const languageStore = useLanguageStore()
const isMobile = computed(() => isMobileDevice())

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      languageLevels: [],
      contentTypes: [],
      topics: [],
      materials: []
    })
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'search'])

// 响应式数据
const showDialog = ref(false)
const loading = ref(false)
const allFilters = ref([])
const allDimensions = ref([])

// 临时选择状态（用于取消操作）
const tempSelection = ref({
  languageLevels: [],
  contentTypes: [],
  topics: [],
  materials: []
})

// 可用维度（动态获取）
const availableDimensions = computed(() => {
  const currentLanguage = languageStore.currentLearningLanguage || 'ja'

  return allDimensions.value.filter(dimension => {
    // 检查该维度下是否有可用的过滤器
    const hasFilters = allFilters.value.some(filter =>
      filter.type === dimension.type &&
      (filter.languages === null || filter.languages.length === 0 || filter.languages.includes(currentLanguage))
    )
    return hasFilters
  })
})

// 获取指定维度的过滤器
const getDimensionFilters = (dimensionType) => {
  const currentLanguage = languageStore.currentLearningLanguage || 'ja'
  return allFilters.value.filter(filter =>
    filter.type === dimensionType &&
    (filter.languages === null || filter.languages.length === 0 || filter.languages.includes(currentLanguage))
  ).sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
}

// 已选择的总数量
const selectedCount = computed(() => {
  return Object.values(tempSelection.value).reduce((total, arr) => total + arr.length, 0)
})

// 方法
const loadFilters = async () => {
  loading.value = true
  try {
    const currentLanguage = languageStore.currentLearningLanguage || 'ja'

    // 同时加载过滤器和维度
    const [filtersResponse, dimensionsResponse] = await Promise.all([
      filterService.getFilters({ language: currentLanguage }),
      dimensionService.getAllDimensions()
    ])

    // 处理过滤器数据
    if (filtersResponse.success) {
      const flatFilters = []
      Object.values(filtersResponse.data || {}).forEach(typeFilters => {
        if (Array.isArray(typeFilters)) {
          flatFilters.push(...typeFilters)
        }
      })
      allFilters.value = flatFilters
    }

    // 处理维度数据
    if (dimensionsResponse.success) {
      allDimensions.value = dimensionsResponse.dimensions || []
    }

  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取指定维度已选数量
const getDimensionSelectedCount = (dimensionType) => {
  const typeMapping = {
    'language_level': 'languageLevels',
    'content_type': 'contentTypes',
    'topic': 'topics',
    'material': 'materials'
  }
  const localType = typeMapping[dimensionType]
  return localType ? tempSelection.value[localType].length : 0
}

// 判断过滤器是否被选中
const isSelected = (filterId) => {
  // 在所有维度中查找该过滤器
  return Object.values(tempSelection.value).some(arr => arr.includes(filterId))
}

// 切换过滤器选择
const toggleFilter = (filterId, dimensionType) => {
  const typeMapping = {
    'language_level': 'languageLevels',
    'content_type': 'contentTypes',
    'topic': 'topics',
    'material': 'materials'
  }
  const localType = typeMapping[dimensionType]

  if (localType) {
    const currentIds = [...tempSelection.value[localType]]
    const index = currentIds.indexOf(filterId)

    if (index > -1) {
      currentIds.splice(index, 1)
    } else {
      currentIds.push(filterId)
    }

    tempSelection.value[localType] = currentIds
  }
}

// 取消选择
const cancelSelection = () => {
  showDialog.value = false
  // 恢复到原始状态
  tempSelection.value = {
    languageLevels: [...props.modelValue.languageLevels],
    contentTypes: [...props.modelValue.contentTypes],
    topics: [...props.modelValue.topics],
    materials: [...props.modelValue.materials]
  }
}

// 清空选择
const clearSelection = () => {
  tempSelection.value = {
    languageLevels: [],
    contentTypes: [],
    topics: [],
    materials: []
  }
  // 只清空选择，不关闭弹框，不触发搜索
}

// 确定选择
const confirmSelection = () => {
  showDialog.value = false

  // 发出更新事件
  emit('update:modelValue', { ...tempSelection.value })

  // 触发搜索
  emit('search')
}

// 处理额外按钮点击
const handleExtraButton = (eventData) => {
  // eventData = { button, index }
  const { button } = eventData
  if (button.text === '清空') {
    clearSelection()
  }
}

// 监听props变化，同步到临时选择
watch(() => props.modelValue, (newVal) => {
  tempSelection.value = {
    languageLevels: [...(newVal.languageLevels || [])],
    contentTypes: [...(newVal.contentTypes || [])],
    topics: [...(newVal.topics || [])],
    materials: [...(newVal.materials || [])]
  }
}, { immediate: true, deep: true })

// 监听弹窗显示，重置临时选择
watch(showDialog, (show) => {
  if (show) {
    tempSelection.value = {
      languageLevels: [...props.modelValue.languageLevels],
      contentTypes: [...props.modelValue.contentTypes],
      topics: [...props.modelValue.topics],
      materials: [...props.modelValue.materials]
    }
  }
})

// 监听语言变化，重新加载筛选项
watch(() => languageStore.currentLearningLanguage, async (newLanguage, oldLanguage) => {
  if (newLanguage !== oldLanguage) {
    // 语言变化时重新加载筛选项数据
    await loadFilters()
    
    // 清空当前选择（因为不同语言的筛选项不同）
    tempSelection.value = {
      languageLevels: [],
      contentTypes: [],
      topics: [],
      materials: []
    }
    
    // 通知父组件清空筛选条件
    emit('update:modelValue', { ...tempSelection.value })
  }
})

// 组件挂载时加载数据
onMounted(() => {
  loadFilters()
})
</script>

<style scoped>
.filter-dialog-wrapper {
  display: inline-block;
}

.filter-trigger-btn {
  position: relative;
}

/* 弹窗内容 */
.dialog-content {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  /* 让StandardDialog的standard-dialog-content处理滚动 */
}

.drawer-content {
  display: flex;
  flex-direction: column;
  min-height: 450px;
  max-height: 600px;
  overflow-y: auto;
}

.selection-summary {
  margin-bottom: 16px;
  padding: 8px 12px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  text-align: center;
}

.total-selected {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
}

.selected-hint {
  font-size: 14px;
  font-weight: normal;
  color: var(--el-color-primary);
}

/* 所有维度展示 */
.all-dimensions-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dimension-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-content: flex-start;
  padding: 4px;
}

.filter-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  font-size: 14px;
}

.filter-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-tag.selected {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 底部操作栏（只保留抽屉的） */
.drawer-footer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  margin: 0 -16px -16px -16px;
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
}

.drawer-footer .footer-info {
  text-align: center;
}

.drawer-footer .total-selected {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.drawer-footer .footer-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 手机端样式 */
.mobile-dimensions {
  gap: 20px;
}

.mobile-dimension-section {
  gap: 10px;
}

.mobile-tags {
  gap: 6px;
}

.mobile-filter-tag {
  font-size: 13px;
  padding: 4px 8px;
}

.drawer-footer {
  padding: 16px;
  margin: 0 -16px -16px -16px;
  background: var(--el-bg-color);
}

.footer-btn {
  flex: 1;
  max-width: 100px;
}


</style>
