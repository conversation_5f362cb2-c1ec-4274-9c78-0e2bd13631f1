<!--
  过滤器编辑对话框
  用于编辑现有过滤器
-->
<template>
  <StandardDialog v-model="dialogVisible" title="编辑过滤器" width="600px" :show-confirm="true" confirm-text="保存"
    cancel-text="取消" @confirm="handleSave" @close="handleClose">
    <div class="filter-edit-form" v-loading="loading">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="80px" label-position="left">
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入过滤器名称" maxlength="50" show-word-limit />
        </el-form-item>

        <el-form-item label="键值" prop="key">
          <el-input v-model="formData.key" placeholder="请输入过滤器键值（英文）" maxlength="50" show-word-limit />
          <div class="form-tip">
            键值用于程序识别，建议使用英文和下划线，如：n5_level
          </div>
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择或输入过滤器类型" style="width: 100%" filterable allow-create
            default-first-option>
            <el-option v-for="type in availableTypes" :key="type.key" :label="type.name" :value="type.key" />
          </el-select>
          <div class="form-tip">
            选择现有维度类型或输入新的维度类型
          </div>
        </el-form-item>

        <el-form-item label="语言" prop="languages">
          <el-select v-model="selectedLanguages" placeholder="请选择适用语言（可多选）" style="width: 100%" multiple clearable
            collapse-tags collapse-tags-tooltip>
            <el-option label="日语" value="ja" />
            <el-option label="英语" value="en" />
            <el-option label="中文简体" value="zh-CN" />
            <el-option label="中文繁体" value="zh-TW" />
          </el-select>
          <div class="form-tip">
            不选择任何语言表示适用于所有语言
          </div>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入过滤器描述（可选）" maxlength="200"
            show-word-limit />
        </el-form-item>

        <el-form-item label="状态" prop="isActive">
          <el-switch v-model="formData.isActive" active-text="启用" inactive-text="禁用" />
          <div class="form-tip">
            禁用的过滤器不会在筛选界面显示
          </div>
        </el-form-item>
      </el-form>
    </div>
  </StandardDialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import StandardDialog from '@/components/common/StandardDialog.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  filter: {
    type: Object,
    default: null
  },
  filterTypes: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

// 响应式数据
const loading = ref(false)
const formRef = ref(null)
const formData = ref({
  name: '',
  key: '',
  type: '',
  languages: [],
  description: '',
  isActive: true
})

// 单独管理 languages，避免响应式问题
const selectedLanguages = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const availableTypes = computed(() => {
  return props.filterTypes.filter(type => type.key !== 'all')
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入过滤器名称', trigger: 'blur' },
    { min: 1, max: 50, message: '名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  key: [
    { required: true, message: '请输入过滤器键值', trigger: 'blur' },
    { min: 1, max: 50, message: '键值长度在 1 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '键值只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择过滤器类型', trigger: 'change' }
  ]
}

// 监听对话框打开状态，只在打开时初始化数据
watch(() => props.modelValue, (isVisible) => {
  if (isVisible && props.filter) {
    // 只在对话框打开时初始化表单数据
    formData.value = {
      name: props.filter.name || '',
      key: props.filter.key || '',
      type: props.filter.type || '',
      languages: [], // 不再使用这个字段
      description: props.filter.description || '',
      isActive: props.filter.isActive !== false
    }

    // 单独初始化语言选择
    if (props.filter.languages && Array.isArray(props.filter.languages)) {
      selectedLanguages.value = props.filter.languages.slice()
    } else {
      selectedLanguages.value = []
    }
  }
})

// 处理保存
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构建保存数据，使用独立的 selectedLanguages
    const saveData = {
      name: formData.value.name,
      key: formData.value.key,
      type: formData.value.type,
      languages: selectedLanguages.value.slice(), // 使用独立管理的语言数组
      description: formData.value.description,
      isActive: formData.value.isActive
    }

    emit('save', saveData)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  }
}

// 处理关闭
const handleClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.filter-edit-form {
  padding: 0.5rem 0;
}

.form-tip {
  font-size: 0.75rem;
  color: var(--el-text-color-secondary);
  margin-top: 0.25rem;
  line-height: 1.4;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
