/**
 * 文本内容节点处理器
 * 合并了文本节点和分句节点的功能
 */

import { md5 } from "../../utils/md5";
import { detectLanguage } from "../../utils/languageDetection";
import { getMappingsSync } from "../../services/speakerMappingService";
import {
  generateUniqueSegmentId,
  ensureUniqueSegmentIds,
} from "../../utils/segmentIdGenerator";

/**
 * 解析说话人标记
 * @param {string} text - 文本内容
 * @returns {Object} 解析结果，包含说话人标记和实际内容
 */
function parseSpeaker(text) {
  // 匹配说话人标记（支持字母、数字、中文，后跟全角或半角冒号）
  const speakerRegex = /^([A-Za-z0-9\u4e00-\u9fff]+)[：:]\s*/;
  const match = text.match(speakerRegex);

  if (match) {
    return {
      speaker: match[1],
      content: text.substring(match[0].length),
    };
  }

  return {
    speaker: null,
    content: text,
  };
}

/**
 * 对分句进行语言检测
 * @param {Array} segments - 分句数组
 * @returns {Array} 带有语言信息的分句数组
 */
function detectLanguagesForSegments(segments) {
  return segments.map((segment) => {
    // 如果分句已有语言设置，保留原有语言
    if (segment.language && segment.language !== "auto") {
      return segment;
    }

    // 对内容进行语言检测
    const language = segment.content ? detectLanguage(segment.content) : "auto";

    return {
      ...segment,
      language,
    };
  });
}

/**
 * 处理分句，添加说话人信息和voice_db_id
 * @param {Array} segments - 分句数组
 * @param {boolean} enableSpeakerParsing - 是否启用说话人解析
 * @returns {Array} 处理后的分句数组
 */
function processSegmentsWithSpeakers(segments, enableSpeakerParsing) {
  if (!enableSpeakerParsing) {
    return segments;
  }

  return segments.map((segment) => {
    // 获取voice_db_id的辅助函数 - 确保始终能返回有效的ID
    const getVoiceDbId = (speaker, language) => {
      try {
        // 获取合并后的映射（用户映射 + 默认映射）
        const mappings = getMappingsSync();
        const langMappings = mappings[language];

        // 如果没有该语言的映射，使用日语作为后备
        if (!langMappings) {
          const fallbackLang = "ja";
          const fallbackMappings = mappings[fallbackLang];
          if (fallbackMappings && fallbackMappings.default) {
            console.log(
              `未找到语言 "${language}" 的映射，使用 "${fallbackLang}" 的默认映射`
            );
            return fallbackMappings.default;
          }
          console.error(
            `无法为语言 "${language}" 找到映射，且后备语言也无映射`
          );
          return null;
        }

        const speakerKey = speaker || "default";

        // 1. 查找特定说话人映射
        if (langMappings[speakerKey]) {
          return langMappings[speakerKey];
        }

        // 2. 如果找不到特定说话人，使用默认映射
        if (langMappings.default) {
          return langMappings.default;
        }

        // 3. 如果连默认映射都没有，尝试使用该语言的第一个可用映射
        const firstAvailableKey = Object.keys(langMappings)[0];
        if (firstAvailableKey) {
          console.log(
            `未找到默认映射，使用语言 "${language}" 的第一个可用映射: ${firstAvailableKey}`
          );
          return langMappings[firstAvailableKey];
        }

        // 4. 实在找不到，返回null（理论上不应该发生）
        console.error(
          `无法为说话人 "${speaker}" 和语言 "${language}" 找到任何映射`
        );
        return null;
      } catch (error) {
        console.error("获取voice_db_id失败:", error);
        return null;
      }
    };

    // 如果是转场文本，自动将说话人设置为default
    if (segment.type === "transition") {
      const voiceDbId = getVoiceDbId("default", segment.language);
      return {
        ...segment,
        speaker: "default",
        voice_db_id: voiceDbId,
      };
    }

    // 如果分句已经有 speaker 信息（来自恢复的设置），直接使用它计算 voice_db_id
    if (segment.speaker !== undefined && segment.speaker !== null) {
      const voiceDbId = getVoiceDbId(segment.speaker, segment.language);
      return {
        ...segment,
        voice_db_id: voiceDbId,
      };
    }

    // 解析说话人标记
    const { speaker, content } = parseSpeaker(segment.content);

    // 如果有说话人标记，更新分句内容和添加说话人信息
    if (speaker) {
      const voiceDbId = getVoiceDbId(speaker, segment.language);

      return {
        ...segment,
        content,
        speaker: speaker, // 保持原始说话人标记
        voice_db_id: voiceDbId, // 添加数据库声音ID
      };
    }

    // 没有说话人标记的情况，使用default
    const voiceDbId = getVoiceDbId("default", segment.language);
    return {
      ...segment,
      voice_db_id: voiceDbId,
    };
  });
}

/**
 * 处理文本内容节点
 * @param {Object} node - 节点对象
 * @returns {Object} 处理结果
 */
function textContentNodeProcessor(node) {
  // 检查节点参数
  if (!node.params || !node.params.text) {
    return {
      text: "",
      segments: [],
      isEmpty: true,
    };
  }

  const text = node.params.text;
  const mode = node.params.mode || "paragraph";

  // 获取说话人解析设置
  const enableSpeakerParsing = node.params.enableSpeakerParsing || false;

  // 极简方案：如果已有分句数据，直接返回现有分句
  if (node.params.segments && node.params.segments.length > 0) {
    // 直接使用现有分句，不进行任何匹配或合并
    const existingSegments = node.params.segments;

    // 进行语言检测（保留手动设置的语言）
    const segmentsWithLanguage = detectLanguagesForSegments(existingSegments);

    // 处理说话人信息
    const finalResult = enableSpeakerParsing
      ? processSegmentsWithSpeakers(segmentsWithLanguage, enableSpeakerParsing)
      : segmentsWithLanguage;

    return {
      text,
      segments: finalResult,
      isEmpty: text.trim() === "",
      isCustom: true,
      hasSpeakers: enableSpeakerParsing,
    };
  }

  // 分句处理
  let segments = [];

  if (mode === "none") {
    // 不分句，整个文本作为一个分句
    segments = [
      {
        id: generateUniqueSegmentId(text, []),
        content: text,
        type: "normal",
        difficulty: "normal", // 默认为普通难度
        keywords: [],
      },
    ];
  } else if (mode === "paragraph") {
    // 按段落分句
    const splitBy = node.params.splitBy || "\n";
    // 先创建分句
    const rawSegments = text
      .split(splitBy)
      .map((content) => content)
      .filter((content) => content)
      .map((content) => {
        return {
          id: "", // 临时ID，稍后统一生成唯一ID
          content,
          type: "normal", // 默认为普通文本类型
          difficulty: "normal", // 默认为普通难度
          keywords: [], // 添加关键词数组字段
        };
      });

    // 统一进行语言检测
    const segmentsWithLanguage = detectLanguagesForSegments(rawSegments);

    // 确保ID唯一
    segments = ensureUniqueSegmentIds(segmentsWithLanguage);
  } else if (mode === "period") {
    // 按句号分句
    // 日语：。
    // 中文：。
    // 英文：.
    const periodRegex = /([。.！!？?]+)/g;
    const parts = text.split(periodRegex);

    // 先创建分句
    let rawSegments = [];
    let currentSegment = "";

    for (let i = 0; i < parts.length; i++) {
      currentSegment += parts[i];

      // 如果当前部分是句号，并且不是最后一个部分
      if (periodRegex.test(parts[i]) && i < parts.length - 1) {
        if (currentSegment) {
          const content = currentSegment;
          rawSegments.push({
            id: "", // 临时ID，稍后统一生成唯一ID
            content,
            type: "normal", // 默认为普通文本类型
            keywords: [], // 添加关键词数组字段
          });
        }
        currentSegment = "";
      }
    }

    // 处理最后一个部分
    if (currentSegment) {
      const content = currentSegment;
      rawSegments.push({
        id: "", // 临时ID，稍后统一生成唯一ID
        content,
        type: "normal", // 默认为普通文本类型
        difficulty: "normal", // 默认为普通难度
        keywords: [], // 添加关键词数组字段
      });
    }

    // 统一进行语言检测
    const segmentsWithLanguage = detectLanguagesForSegments(rawSegments);

    // 确保ID唯一
    segments = ensureUniqueSegmentIds(segmentsWithLanguage);
  }

  // 如果没有分句结果，创建一个包含整个文本的段落
  if (segments.length === 0) {
    // 创建分句并进行语言检测
    const rawSegments = detectLanguagesForSegments([
      {
        id: "", // 临时ID，稍后统一生成唯一ID
        content: text,
        type: "normal", // 默认为普通文本类型
        difficulty: "normal", // 默认为普通难度
        keywords: [], // 添加关键词数组字段
      },
    ]);

    // 确保ID唯一
    segments = ensureUniqueSegmentIds(rawSegments);
  }

  // 处理说话人信息
  if (enableSpeakerParsing) {
    segments = processSegmentsWithSpeakers(segments, enableSpeakerParsing);
  }

  // 统一更新节点参数中的segments字段
  node.params.segments = [...segments];

  return {
    text,
    segments,
    isEmpty: text.trim() === "",
    hasSpeakers: enableSpeakerParsing,
  };
}

export { parseSpeaker };
export default textContentNodeProcessor;
