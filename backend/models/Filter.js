const { DataTypes, Op } = require('sequelize');

module.exports = (sequelize) => {
  const Filter = sequelize.define('Filter', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '过滤器名称'
    },
    key: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: '过滤器唯一标识符'
    },
    type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '过滤器类型：language_level(语言等级)、content_type(内容类型)、topic(主题)、material(教材)、skill(技能)等，支持自定义'
    },
    languages: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '适用语言代码列表，JSON格式存储，如["ja","en"]，null表示通用',
      get() {
        const value = this.getDataValue('languages');
        if (!value) return [];
        try {
          return JSON.parse(value);
        } catch (e) {
          return [];
        }
      },
      set(value) {
        if (Array.isArray(value)) {
          this.setDataValue('languages', JSON.stringify(value));
        } else {
          this.setDataValue('languages', null);
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '过滤器描述'
    },

    sortOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'sort_order',
      comment: '排序顺序'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      field: 'is_active',
      comment: '是否激活'
    },
    isSystem: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_system',
      comment: '是否为系统过滤器（不可删除）'
    }
  }, {
    tableName: 'filters',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['type']
      },
      {
        fields: ['language']
      },
      {
        fields: ['type', 'language']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  // 定义关联关系
  Filter.associate = function(models) {
    // 过滤器可以关联多个内容
    Filter.belongsToMany(models.Content, {
      through: 'content_filters',
      foreignKey: 'filter_id',
      otherKey: 'content_id',
      as: 'contents'
    });

    // 过滤器属于某个过滤器类型
    Filter.belongsTo(models.FilterType, {
      foreignKey: 'type',
      targetKey: 'type',
      as: 'filterType'
    });
  };

  // 静态方法：按类型获取过滤器
  Filter.getByType = async function(type, language = null) {
    const whereClause = {
      type: type,
      isActive: true
    };

    // 如果指定了语言，过滤适用的过滤器
    if (language) {
      whereClause[Op.or] = [
        { languages: null },
        { languages: '' },
        { languages: { [Op.like]: `%"${language}"%` } }
      ];
    }

    return await Filter.findAll({
      where: whereClause,
      order: [['sortOrder', 'ASC'], ['name', 'ASC']]
    });
  };

  // 静态方法：按语言获取所有过滤器
  Filter.getByLanguage = async function(language) {
    return await Filter.findAll({
      where: {
        [Op.or]: [
          { languages: null },
          { languages: '' },
          { languages: { [Op.like]: `%"${language}"%` } }
        ],
        isActive: true
      },
      order: [['type', 'ASC'], ['sortOrder', 'ASC'], ['name', 'ASC']]
    });
  };

  // 静态方法：获取过滤器分组
  Filter.getGrouped = async function(language = null) {
    const whereClause = {
      isActive: true
    };

    if (language) {
      whereClause[Op.or] = [
        { languages: null },
        { languages: '' },
        { languages: { [Op.like]: `%"${language}"%` } }
      ];
    }

    const filters = await Filter.findAll({
      where: whereClause,
      order: [['type', 'ASC'], ['sortOrder', 'ASC'], ['name', 'ASC']]
    });

    // 按类型分组
    const grouped = {};
    filters.forEach(filter => {
      if (!grouped[filter.type]) {
        grouped[filter.type] = [];
      }
      grouped[filter.type].push(filter);
    });

    return grouped;
  };

  // 静态方法：获取语言等级过滤器
  Filter.getLanguageLevels = async function(language) {
    return await Filter.getByType('language_level', language);
  };

  // 静态方法：获取内容类型过滤器
  Filter.getContentTypes = async function() {
    return await Filter.getByType('content_type');
  };

  // 静态方法：获取主题过滤器
  Filter.getTopics = async function() {
    return await Filter.getByType('topic');
  };

  // 静态方法：获取教材过滤器
  Filter.getMaterials = async function(language) {
    return await Filter.getByType('material', language);
  };

  return Filter;
};
